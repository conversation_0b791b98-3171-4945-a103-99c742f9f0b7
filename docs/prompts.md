完整的图片编辑 Prompts 系统方案

  用户交互流程

  用户进入 /prompts 页面
      ↓
  浏览图片对比卡片（原图 → 效果图）
      ↓
  用户可以选择：
  ├── 🔍 搜索特定效果
  ├── 📋 直接复制 prompt（快速操作）
  └── 👁️ 点击卡片标题/图片 → 打开详情模态框
      ↓
      详情模态框显示：
      ├── 📷 多组前后对比示例
      ├── 📋 完整 prompt 内容
      ├── 💡 详细使用技巧
      ├── 🤖 兼容的AI模型列表
      ├── 📥 下载示例图片
      └── 🔗 推荐相关 prompts
      ↓
      用户可以：
      ├── 📋 复制完整 prompt
      ├── 📷 下载示例图片作为参考
      ├── 🔗 分享这个 prompt
      └── ✕ 关闭返回列表

  卡片设计（列表页）

  ┌─────────────────────┐
  │🖼️ BEFORE → AFTER    │
  │┌─────────┬─────────┐│
  ││  原图📸  │  效果✨  ││  <- 可点击打开详情
  ││         │         ││
  │└─────────┴─────────┘│
  │                     │
  │✨ Photo Enhancement │  <- 可点击打开详情
  │                     │
  │Boost colors and     │
  │improve lighting...  │
  │                     │
  │👁️ 2.1k  📋 Copy    │  <- 直接复制按钮
  └─────────────────────┘

  详情模态框完整设计

  ┌─────────────────────────────────────────────────────────────────────┐
  │                              ✕ Close                               │
  ├─────────────────────────────────────────────────────────────────────┤
  │                                                                     │
  │  ✨ Professional Photo Enhancement                                  │
  │  👁️ 2,134 views  📋 1,456 copies                                   │
  │                                                                     │
  │  🖼️ Example Results:                                                │
  │  ┌─────────────────────────────────────────────────────────────────┐ │
  │  │ Example 1: Landscape Enhancement                                │ │
  │  │ ┌─────────────┐        ➜        ┌─────────────┐                │ │
  │  │ │[暗淡风景照片] │                 │[鲜艳风景图片] │                │ │
  │  │ │  🌄📸       │                 │   🌄✨       │                │ │
  │  │ └─────────────┘                 └─────────────┘                │ │
  │  │                                                                 │ │
  │  │ Example 2: Portrait Enhancement                                 │ │
  │  │ ┌─────────────┐        ➜        ┌─────────────┐                │ │
  │  │ │[普通人像照片] │                 │[专业人像效果] │                │ │
  │  │ │  👤📸       │                 │   👤✨       │                │ │
  │  │ └─────────────┘                 └─────────────┘                │ │
  │  │                                                                 │ │
  │  │        📥 [Download All Examples]                               │ │
  │  └─────────────────────────────────────────────────────────────────┘ │
  │                                                                     │
  │  📝 Description:                                                    │
  │  This nano banana prompt enhances photo quality by boosting colors,│
  │  improving lighting, and increasing overall clarity. Perfect for    │
  │  bringing life to dull or underexposed photographs.                │
  │                                                                     │
  │  ┌─────────────────────────────────────────────────────────────────┐ │
  │  │📋 Complete Nano Banana Prompt:                                 │ │
  │  │                                                                 │ │
  │  │This photo is very boring and plain. Enhance it! Increase the   │ │
  │  │contrast, boost the colors, and improve the lighting to make    │ │
  │  │it richer. You can crop and delete details that affect the     │ │
  │  │composition. Make sure the enhanced image looks natural and     │ │
  │  │professional while maintaining the original subject and style.  │ │
  │  │Focus on enhancing the existing beauty rather than changing     │ │
  │  │the core composition or adding new elements.                    │ │
  │  │                                                                 │ │
  │  └─────────────────────────────────────────────────────────────────┘ │
  │                                                                     │
  │     📋 [Copy Full Prompt]  🔗 [Share]  ⭐ [Add to Favorites]       │
  │                                                                     │
  │  💡 Pro Usage Tips:                                                 │
  │     • Works best with underexposed or flat-colored photos          │
  │     • Ideal for landscape, portrait, and architectural photography │
  │     • Avoid using on already heavily processed images              │
  │     • For best results, use high-resolution source images          │
  │                                                                     │
  │  🤖 Compatible AI Models:                                           │
  │     ✅ Gemini-2.5-Flash-Image-Preview  ✅ Claude-3.5-Sonnet        │
  │     ✅ GPT-4V                         ✅ Midjourney V6             │
  │                                                                     │
  │  🔥 You might also like:                                            │
  │     • Portrait Skin Enhancement      • Night Photo Brightening     │
  │     • Vintage Photo Restoration      • Creative Color Grading      │
  │                                                                     │
  └─────────────────────────────────────────────────────────────────────┘

  数据库结构（完整版）

  CREATE TABLE nano_prompts (
    id INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    uuid VARCHAR(255) NOT NULL UNIQUE,
    title VARCHAR(255) NOT NULL,
    category VARCHAR(255) NULL,
    author VARCHAR(255) NULL,
    description TEXT,
    prompt_content TEXT NOT NULL,
    emoji VARCHAR(10),

    -- 图片展示
    before_image_url VARCHAR(500),      -- 主要的原图
    after_image_url VARCHAR(500),       -- 主要的效果图
    example_images JSON,                -- 多个示例 [{"before": "url", "after": "url",
  "title": "Example 1"}]

    -- 使用指南
    usage_tips TEXT,                    -- 使用技巧
    best_for TEXT,                      -- 适用场景
    compatible_models JSON,             -- ["Gemini-2.5-Flash", "Claude-3.5"]

    -- 统计
    views INTEGER NOT NULL DEFAULT 0,
    copy_count INTEGER NOT NULL DEFAULT 0,

    -- 系统字段
    status VARCHAR(50) NOT NULL DEFAULT 'online',
    sort INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    locale VARCHAR(50) DEFAULT 'en'
  );