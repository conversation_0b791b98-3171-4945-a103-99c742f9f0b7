# Nano Banana Prompts MVP 开发任务清单

## 🎯 MVP 目标
创建一个简单可用的图片编辑 prompts 展示平台，核心功能：
- 展示图片对比卡片 (原图 → 效果图)
- 一键复制 prompt 功能
- 详情模态框查看完整信息
- 基础搜索功能

**预计工作量**: 1-2 周

---

## 📋 MVP 核心任务 (按执行顺序)

### 阶段 1: 数据基础 (1-2 天)

#### [ ] **1.1 数据库表设计** 
- 在 `src/db/schema.ts` 添加 `nano_prompts` 表
- 执行数据库迁移
- 准备 5-10 个示例数据

#### [ ] **1.2 数据模型**
- 创建 `src/models/nano-prompt.ts`
- 实现基础 CRUD 函数：
  - `getAllNanoPrompts()`
  - `findNanoPromptByUuid()`
  - `incrementCopyCount()`

#### [ ] **1.3 TypeScript 类型**
- 创建 `src/types/nano-prompt.ts`
- 定义核心接口：`NanoPrompt`, `PromptsResponse`

---

### 阶段 2: API 接口 (1-2 天)

#### [ ] **2.1 列表接口**
- 创建 `src/app/api/prompts/route.ts`
- GET 方法支持基础分页和搜索
- 返回格式：`{data: [], pagination: {}}`

#### [ ] **2.2 详情接口**  
- 创建 `src/app/api/prompts/[uuid]/route.ts`
- GET 方法获取单个 prompt

#### [ ] **2.3 复制统计**
- 创建 `src/app/api/prompts/[uuid]/copy/route.ts`
- POST 方法记录复制次数

---

### 阶段 3: 核心组件 (2-3 天)

#### [ ] **3.1 图片对比组件**
- 创建 `src/components/prompts/ImageComparison.tsx`
- Before/After 图片展示
- 加载状态和错误处理

#### [ ] **3.2 Prompt 卡片**
- 创建 `src/components/prompts/PromptCard.tsx`
- 集成图片对比组件
- 添加标题、描述、复制按钮
- 点击事件处理

#### [ ] **3.3 详情模态框**
- 创建 `src/components/prompts/PromptModal.tsx`
- 使用 shadcn/ui Dialog 组件
- 显示完整 prompt 内容和示例
- 复制和关闭功能

---

### 阶段 4: 页面集成 (1-2 天)

#### [ ] **4.1 数据获取 Hook**
- 创建 `src/hooks/usePrompts.ts`
- 管理列表数据和加载状态
- 搜索功能集成

#### [ ] **4.2 模态框 Hook**
- 创建 `src/hooks/usePromptModal.ts`
- 管理模态框开关和选中项

#### [ ] **4.3 主页面**
- 创建 `src/app/[locale]/prompts/page.tsx`
- 网格布局展示卡片
- 集成搜索框和模态框
- 基础响应式设计

---

### 阶段 5: 基础功能完善 (1 天)

#### [ ] **5.1 搜索组件**
- 创建 `src/components/prompts/PromptSearch.tsx`
- 简单的输入框和搜索按钮
- 防抖处理

#### [ ] **5.2 工具函数**
- 创建 `src/lib/prompt-utils.ts`
- 复制到剪贴板功能
- Toast 提示集成

#### [ ] **5.3 移动端基础适配**
- 卡片组件移动端布局
- 模态框移动端显示优化

---

## 🔧 技术要求

### 必须使用的技术栈
- **数据库**: 现有 Drizzle ORM + PostgreSQL
- **UI 组件**: shadcn/ui (Dialog, Button, Input)  
- **图片处理**: Next.js Image 组件
- **状态管理**: React useState + custom hooks

### 数据库表结构 (简化版)
```sql
CREATE TABLE nano_prompts (
  id INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  uuid VARCHAR(255) NOT NULL UNIQUE,
  title VARCHAR(255) NOT NULL,
  category VARCHAR(255) NULL,
  author VARCHAR(255) NULL,
  description TEXT,
  prompt_content TEXT NOT NULL,
  before_image_url VARCHAR(500),
  after_image_url VARCHAR(500),
  views INTEGER DEFAULT 0,
  copy_count INTEGER DEFAULT 0,
  status VARCHAR(50) DEFAULT 'online',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

---

## 📱 MVP 功能范围

### ✅ 包含功能
- 图片对比展示 (Before → After)
- Prompt 列表展示（网格布局）
- 一键复制 prompt 功能
- 详情模态框（完整内容）
- 基础搜索（按标题搜索）
- 复制次数统计
- 移动端基础适配

### ❌ 暂不包含
- 分类筛选
- 作者筛选  
- 多图片示例展示
- 使用技巧详情
- 兼容模型显示
- 分享功能
- 收藏功能
- 无限滚动
- 图片懒加载
- 高级搜索
- 用户认证

---

## 🎨 UI 设计要求

### 列表页布局
```
桌面端: 3 列网格
平板端: 2 列网格  
手机端: 1 列全宽
```

### 卡片设计
```
┌─────────────────────┐
│🖼️ [原图] → [效果图] │
│✨ Prompt Title      │
│Short description... │
│👁️ 1.2k  📋 Copy    │
└─────────────────────┘
```

### 模态框设计
```
- 完整标题和描述
- 图片对比展示  
- 完整 prompt 内容框
- 复制按钮
- 关闭按钮
```

---

## ✅ MVP 完成标准

### 功能要求
- [ ] 能展示 prompt 列表
- [ ] 图片正常加载和显示
- [ ] 复制功能正常工作
- [ ] 搜索能找到相关内容
- [ ] 模态框正常打开关闭
- [ ] 移动端基本可用

### 性能要求
- [ ] 页面加载时间 < 3 秒
- [ ] 图片加载有合理的占位符
- [ ] 没有明显的 UI 错误

### 数据要求
- [ ] 至少有 5 个示例 prompts
- [ ] 图片链接可正常访问
- [ ] 数据库查询正常

---

## 🚀 发布检查清单

### 代码质量
- [ ] 所有 TypeScript 类型正确
- [ ] 没有 console.error 错误
- [ ] 组件按现有项目规范组织

### 功能测试
- [ ] 桌面端功能正常
- [ ] 移动端基本可用  
- [ ] 复制功能在不同浏览器正常
- [ ] 模态框在不同设备正常显示

### 集成测试
- [ ] API 返回数据格式正确
- [ ] 数据库操作无错误
- [ ] 图片加载无 404 错误

---

## 💡 开发建议

1. **先做数据和 API**，再做 UI 组件
2. **使用现有的 shadcn/ui 组件**，减少自定义样式
3. **先实现桌面端**，再适配移动端
4. **图片暂时使用占位符**，后期替换真实图片
5. **复制功能可以先用简单的文本复制**
6. **搜索功能先做简单的标题匹配**

---

## 🔄 任务状态

- **未开始**: [ ]
- **进行中**: [🔄] 
- **已完成**: [✅]
- **需要帮助**: [❓]

**当前进度**: 0/18 任务完成