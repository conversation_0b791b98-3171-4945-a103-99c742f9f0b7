# Nano Banana Prompts 系统开发方案

## 项目概述

基于现有的 Next.js SaaS 架构，开发一个专注于图片编辑的 AI prompts 展示和分享平台。核心功能是通过图片对比展示 (原图 → 效果图) 来帮助用户理解 prompt 的效果，并提供一键复制功能。

## 技术架构

### 前端技术栈
- **框架**: Next.js 15 with App Router
- **UI 库**: Tailwind CSS + shadcn/ui 组件
- **状态管理**: React Context + Custom Hooks
- **图片处理**: Next.js Image 组件 + 云存储优化
- **交互**: 响应式设计 + 移动端适配

### 后端技术栈
- **数据库**: PostgreSQL + Drizzle ORM
- **API**: Next.js API Routes (RESTful 设计)
- **图片存储**: 云存储服务 (建议 Cloudflare R2 或 AWS S3)
- **缓存**: 利用现有缓存机制

### 数据库设计

#### nano_prompts 表
```sql
CREATE TABLE nano_prompts (
  id INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  uuid VARCHAR(255) NOT NULL UNIQUE,
  title VARCHAR(255) NOT NULL,
  category VARCHAR(255) NULL,                 -- 新增：分类字段
  author VARCHAR(255) NULL,                   -- 新增：作者字段
  description TEXT,
  prompt_content TEXT NOT NULL,
  emoji VARCHAR(10),
  
  -- 图片展示字段
  before_image_url VARCHAR(500),              -- 原图 URL
  after_image_url VARCHAR(500),               -- 效果图 URL
  example_images JSON,                        -- 多个示例对比图
  
  -- 使用指南
  usage_tips TEXT,                            -- 使用技巧
  best_for TEXT,                              -- 适用场景
  compatible_models JSON,                     -- 兼容的 AI 模型
  
  -- 统计字段
  views INTEGER NOT NULL DEFAULT 0,
  copy_count INTEGER NOT NULL DEFAULT 0,
  
  -- 系统字段
  status VARCHAR(50) NOT NULL DEFAULT 'online',
  sort INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  locale VARCHAR(50) DEFAULT 'en'
);
```

## 文件结构规划

```
src/
├── app/[locale]/prompts/
│   ├── page.tsx                            # Prompts 列表页面
│   └── loading.tsx                         # 加载状态页面
├── app/api/prompts/
│   ├── route.ts                           # GET /api/prompts (列表接口)
│   ├── [uuid]/
│   │   ├── route.ts                       # GET /api/prompts/[uuid] (详情接口)
│   │   ├── view/route.ts                  # POST /api/prompts/[uuid]/view (浏览统计)
│   │   └── copy/route.ts                  # POST /api/prompts/[uuid]/copy (复制统计)
│   └── popular/route.ts                   # GET /api/prompts/popular (热门接口)
├── components/prompts/
│   ├── PromptCard.tsx                     # Prompt 卡片组件
│   ├── PromptModal.tsx                    # Prompt 详情模态框
│   ├── PromptGrid.tsx                     # 网格容器组件
│   ├── PromptSearch.tsx                   # 搜索组件
│   ├── ImageComparison.tsx                # 图片对比组件
│   └── PromptStats.tsx                    # 统计显示组件
├── db/
│   └── schema.ts                          # 添加 nano_prompts 表定义
├── models/
│   └── nano-prompt.ts                     # Prompt 数据模型和查询函数
├── hooks/
│   ├── usePrompts.ts                      # Prompts 数据获取 hook
│   ├── usePromptModal.ts                  # 模态框状态管理 hook
│   └── usePromptStats.ts                  # 统计相关 hook
├── types/
│   └── nano-prompt.ts                     # Prompt 相关 TypeScript 类型
└── lib/
    ├── prompt-utils.ts                    # Prompt 相关工具函数
    └── image-utils.ts                     # 图片处理工具函数
```

## API 接口设计

### 1. GET /api/prompts
**功能**: 获取 prompts 列表
**参数**:
```typescript
interface PromptsQuery {
  page?: number;          // 页码，默认 1
  limit?: number;         // 每页数量，默认 12
  search?: string;        // 搜索关键词
  category?: string;      // 分类筛选
  author?: string;        // 作者筛选
  locale?: string;        // 语言，默认 'en'
}
```

**响应**:
```typescript
interface PromptsResponse {
  data: NanoPrompt[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}
```

### 2. GET /api/prompts/[uuid]
**功能**: 获取单个 prompt 详情
**响应**: 完整的 NanoPrompt 对象

### 3. POST /api/prompts/[uuid]/view
**功能**: 记录浏览次数
**响应**: 更新后的浏览次数

### 4. POST /api/prompts/[uuid]/copy
**功能**: 记录复制次数
**响应**: 更新后的复制次数

## 组件架构设计

### 页面层 (Page Level)
```tsx
// src/app/[locale]/prompts/page.tsx
export default function PromptsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <PromptSearch onSearch={handleSearch} />
      <PromptGrid prompts={prompts} onPromptClick={openModal} />
      <PromptModal 
        prompt={selectedPrompt} 
        isOpen={modalOpen} 
        onClose={closeModal} 
      />
    </div>
  );
}
```

### 组件层 (Component Level)
```tsx
// src/components/prompts/PromptCard.tsx
interface PromptCardProps {
  prompt: NanoPrompt;
  onCopy: (uuid: string) => void;
  onClick: (prompt: NanoPrompt) => void;
}

// src/components/prompts/PromptModal.tsx  
interface PromptModalProps {
  prompt: NanoPrompt | null;
  isOpen: boolean;
  onClose: () => void;
}
```

## 数据流设计

### 列表页数据流
```
用户访问 /prompts
    ↓
usePrompts Hook 调用 API
    ↓
API 查询数据库获取 prompts 列表
    ↓
渲染 PromptGrid 组件展示卡片
    ↓
用户交互 (搜索/点击/复制)
    ↓
更新状态 + 调用相关 API
```

### 详情模态框数据流
```
用户点击卡片标题/图片
    ↓
usePromptModal Hook 设置选中的 prompt
    ↓
PromptModal 组件渲染详情内容
    ↓
用户复制/分享/关闭
    ↓
调用统计 API + 更新本地状态
```

## 响应式设计方案

### 桌面端 (≥1024px)
- 3 列网格布局
- 卡片尺寸: 约 320px 宽
- 模态框居中显示，最大宽度 800px

### 平板端 (768px - 1023px)  
- 2 列网格布局
- 卡片自适应宽度
- 模态框适配屏幕宽度

### 移动端 (<768px)
- 1 列布局，卡片全宽
- 搜索框固定在顶部
- 模态框全屏显示

## 性能优化策略

### 图片优化
- 使用 Next.js Image 组件自动优化
- 懒加载卡片图片
- 提供多尺寸图片 (thumbnail, medium, full)

### 数据加载优化
- 实现无限滚动或分页加载
- 搜索防抖处理 (300ms 延迟)
- 使用 React Query 或 SWR 缓存数据

### 用户体验优化
- 骨架屏加载状态
- 复制成功的 toast 提示
- 图片加载失败的占位符

## 国际化支持

### 多语言内容
- 支持中英文 prompts
- 页面 UI 文本国际化
- 搜索支持多语言关键词

### 本地化适配
- 根据 locale 参数过滤内容
- 不同语言的 SEO 优化
- 符合本地用户习惯的 UI 设计

## 部署和运维

### 数据库迁移
- 使用 Drizzle Kit 生成迁移文件
- 提供示例数据的 seed 脚本

### 图片存储
- 配置云存储服务
- 设置 CDN 加速访问
- 实现图片上传接口 (管理后台使用)

### 监控和分析
- 集成现有的统计系统
- 监控 prompts 的使用情况
- 性能监控和错误追踪

## 安全考虑

### 输入验证
- API 参数严格验证
- 防止 SQL 注入攻击
- 图片 URL 白名单验证

### 访问控制
- API 频率限制
- 防止恶意刷量
- 用户行为分析

## 测试策略

### 单元测试
- 工具函数测试
- 组件渲染测试
- API 接口测试

### 集成测试
- 页面交互流程测试
- 数据库操作测试
- 图片加载测试

### 用户测试
- 不同设备的兼容性测试
- 加载性能测试
- 用户体验测试