# Nano Banana Prompts 开发任务清单

## 🎯 项目里程碑

### 第一阶段：核心基础 (Week 1-2)
- ✅ 完成数据库表设计
- ✅ 搭建基础 API 架构  
- ✅ 创建核心组件框架

### 第二阶段：功能实现 (Week 3-4)
- ✅ 实现列表页面
- ✅ 完成详情模态框
- ✅ 集成搜索功能

### 第三阶段：优化完善 (Week 5-6)
- ✅ 性能优化
- ✅ 响应式适配
- ✅ 测试和部署

---

## 📋 详细任务列表

### 🗄️ 数据库和模型层
**预估工作量**: 1-2 天

#### 高优先级
- [ ] **DB-001**: 在 `src/db/schema.ts` 中添加 `nano_prompts` 表定义
  - 包含所有字段：uuid, title, category, author, description, prompt_content, emoji
  - 图片字段：before_image_url, after_image_url, example_images (JSON)
  - 使用指南：usage_tips, best_for, compatible_models (JSON)
  - 统计字段：views, copy_count
  - 系统字段：status, sort, created_at, updated_at, locale

- [ ] **DB-002**: 创建数据库迁移文件
  ```bash
  npx drizzle-kit generate --config=src/db/config.ts
  npx drizzle-kit migrate --config=src/db/config.ts
  ```

- [ ] **MODEL-001**: 创建 `src/models/nano-prompt.ts` 文件
  - `insertNanoPrompt()` - 插入新 prompt
  - `updateNanoPrompt()` - 更新 prompt
  - `findNanoPromptByUuid()` - 根据 UUID 查询
  - `getAllNanoPrompts()` - 获取列表，支持分页和搜索
  - `getNanoPromptsByCategory()` - 按分类查询
  - `incrementViews()` - 增加浏览次数
  - `incrementCopyCount()` - 增加复制次数

#### 中优先级
- [ ] **SEED-001**: 创建示例数据脚本
  - 准备 10-20 个示例 prompts
  - 包含不同分类：Photo Enhancement, Style Transfer, Creative Blend
  - 准备对应的示例图片

---

### 🔌 API 接口层
**预估工作量**: 2-3 天

#### 高优先级
- [ ] **API-001**: 创建 `src/app/api/prompts/route.ts`
  - GET 方法：获取 prompts 列表
  - 支持查询参数：page, limit, search, category, author, locale
  - 返回分页信息和数据列表

- [ ] **API-002**: 创建 `src/app/api/prompts/[uuid]/route.ts`
  - GET 方法：获取单个 prompt 详情
  - 自动增加浏览次数

- [ ] **API-003**: 创建 `src/app/api/prompts/[uuid]/copy/route.ts`
  - POST 方法：记录复制行为
  - 增加 copy_count 统计

#### 中优先级
- [ ] **API-004**: 创建 `src/app/api/prompts/[uuid]/view/route.ts`
  - POST 方法：显式记录浏览
  - 用于详情模态框打开统计

- [ ] **API-005**: 创建 `src/app/api/prompts/popular/route.ts`
  - GET 方法：获取热门 prompts
  - 按 views 或 copy_count 排序

#### 低优先级
- [ ] **API-006**: 添加 API 错误处理和验证
  - 参数验证中间件
  - 统一错误响应格式
  - API 频率限制

---

### 🧩 TypeScript 类型定义
**预估工作量**: 0.5-1 天

#### 高优先级
- [ ] **TYPE-001**: 创建 `src/types/nano-prompt.ts`
  ```typescript
  interface NanoPrompt {
    uuid: string;
    title: string;
    category: string | null;
    author: string | null;
    description: string;
    prompt_content: string;
    emoji: string;
    before_image_url: string;
    after_image_url: string;
    example_images: ExampleImage[];
    usage_tips: string;
    best_for: string;
    compatible_models: string[];
    views: number;
    copy_count: number;
    status: string;
    created_at: string;
    updated_at: string;
  }
  
  interface ExampleImage {
    before: string;
    after: string;
    title?: string;
    description?: string;
  }
  
  interface PromptsQuery {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
    author?: string;
    locale?: string;
  }
  
  interface PromptsResponse {
    data: NanoPrompt[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      hasMore: boolean;
    };
  }
  ```

---

### ⚛️ React Hooks
**预估工作量**: 1-2 天

#### 高优先级
- [ ] **HOOK-001**: 创建 `src/hooks/usePrompts.ts`
  - 数据获取和缓存
  - 搜索状态管理
  - 分页和加载更多
  - 错误处理

- [ ] **HOOK-002**: 创建 `src/hooks/usePromptModal.ts`
  - 模态框开关状态
  - 选中 prompt 管理
  - 复制功能集成

#### 中优先级
- [ ] **HOOK-003**: 创建 `src/hooks/usePromptStats.ts`
  - 统计数据更新
  - 本地状态同步
  - 防重复提交

---

### 🧱 UI 组件层
**预估工作量**: 3-4 天

#### 高优先级
- [ ] **COMP-001**: 创建 `src/components/prompts/PromptCard.tsx`
  - 图片对比展示 (Before → After)
  - 标题、描述、统计信息
  - 一键复制按钮
  - 点击打开详情功能
  - 响应式设计

- [ ] **COMP-002**: 创建 `src/components/prompts/PromptModal.tsx`
  - 完整 prompt 内容展示
  - 多个示例图片对比
  - 使用技巧和兼容模型
  - 复制、分享、关闭功能
  - 相关推荐

- [ ] **COMP-003**: 创建 `src/components/prompts/ImageComparison.tsx`
  - Before/After 图片对比组件
  - 加载状态和错误处理
  - 懒加载优化
  - 可重用的图片展示逻辑

#### 中优先级
- [ ] **COMP-004**: 创建 `src/components/prompts/PromptGrid.tsx`
  - 响应式网格布局
  - 骨架屏加载状态
  - 无限滚动或分页
  - 空状态处理

- [ ] **COMP-005**: 创建 `src/components/prompts/PromptSearch.tsx`
  - 搜索输入框
  - 防抖处理 (300ms)
  - 清空搜索功能
  - 搜索建议 (可选)

#### 低优先级
- [ ] **COMP-006**: 创建 `src/components/prompts/PromptStats.tsx`
  - 浏览次数、复制次数展示
  - 动画效果
  - 格式化数字 (1.2k, 2.3M)

---

### 📄 页面层
**预估工作量**: 1-2 天

#### 高优先级
- [ ] **PAGE-001**: 创建 `src/app/[locale]/prompts/page.tsx`
  - 页面布局和结构
  - 集成搜索、网格、模态框组件
  - SEO 优化设置
  - 国际化支持

- [ ] **PAGE-002**: 创建 `src/app/[locale]/prompts/loading.tsx`
  - 页面加载骨架屏
  - 与实际内容结构保持一致

#### 中优先级
- [ ] **PAGE-003**: 更新导航菜单
  - 在主导航中添加 "Browse Prompts" 链接
  - 更新 `src/i18n/pages/landing/en.json` 中的导航配置

---

### 🛠️ 工具函数
**预估工作量**: 0.5-1 天

#### 中优先级
- [ ] **UTIL-001**: 创建 `src/lib/prompt-utils.ts`
  - 复制到剪贴板功能
  - 格式化统计数字
  - 搜索关键词高亮
  - URL 分享生成

- [ ] **UTIL-002**: 创建 `src/lib/image-utils.ts`
  - 图片 URL 验证
  - 缩略图生成
  - 图片加载错误处理
  - 图片尺寸优化

---

### 📱 响应式和移动端适配
**预估工作量**: 1-2 天

#### 高优先级
- [ ] **MOBILE-001**: 移动端 PromptCard 组件适配
  - 单列布局
  - 触摸友好的按钮尺寸
  - 图片展示优化

- [ ] **MOBILE-002**: 移动端 PromptModal 组件适配
  - 全屏或接近全屏显示
  - 滚动优化
  - 手势支持 (下滑关闭)

#### 中优先级
- [ ] **MOBILE-003**: 搜索组件移动端优化
  - 固定在顶部或收起
  - 虚拟键盘适配
  - 搜索历史 (可选)

---

### ⚡ 性能优化
**预估工作量**: 1-2 天

#### 中优先级
- [ ] **PERF-001**: 图片懒加载实现
  - 使用 `next/image` 组件
  - Intersection Observer API
  - 加载占位符

- [ ] **PERF-002**: 数据缓存优化
  - 实现 React Query 或 SWR
  - API 响应缓存策略
  - 本地状态持久化

- [ ] **PERF-003**: 代码分割和懒加载
  - 模态框组件懒加载
  - 路由级别代码分割
  - 第三方库按需加载

#### 低优先级
- [ ] **PERF-004**: 搜索性能优化
  - 防抖和节流
  - 搜索结果缓存
  - 模糊搜索算法优化

---

### 🧪 测试
**预估工作量**: 2-3 天

#### 中优先级
- [ ] **TEST-001**: API 接口测试
  - 单元测试覆盖所有 API
  - 边界条件测试
  - 错误处理测试

- [ ] **TEST-002**: 组件单元测试  
  - PromptCard 组件测试
  - PromptModal 组件测试
  - Hook 函数测试

#### 低优先级
- [ ] **TEST-003**: 集成测试
  - 页面交互流程测试
  - 用户操作路径测试
  - 跨浏览器兼容性测试

- [ ] **TEST-004**: 性能测试
  - 页面加载速度测试
  - 图片加载性能测试
  - 移动端性能测试

---

### 🚀 部署和运维
**预估工作量**: 1 天

#### 低优先级
- [ ] **DEPLOY-001**: 数据库迁移和部署
  - 生产环境数据库更新
  - 示例数据导入
  - 备份策略制定

- [ ] **DEPLOY-002**: CDN 和图片存储配置
  - 配置 Cloudflare R2 或 AWS S3
  - 设置 CDN 加速
  - 图片优化管道

- [ ] **DEPLOY-003**: 监控和分析
  - 集成现有分析系统
  - 性能监控设置
  - 错误追踪配置

---

## 📊 任务优先级说明

### 🔴 高优先级 (必须完成)
核心功能实现，影响 MVP 可用性的任务

### 🟡 中优先级 (建议完成)  
增强用户体验和系统稳定性的任务

### 🟢 低优先级 (时间允许时完成)
优化和完善功能，可以在后续版本中实现

---

## 🎯 里程碑检查清单

### MVP 可发布标准
- [ ] 所有高优先级任务完成
- [ ] 基础功能测试通过
- [ ] 移动端基本可用
- [ ] 至少 10 个示例 prompts

### 产品完善标准  
- [ ] 所有中优先级任务完成
- [ ] 性能优化达标
- [ ] 完整的用户体验
- [ ] 错误处理完善

### 产品优化标准
- [ ] 所有任务完成
- [ ] 测试覆盖率 > 80%
- [ ] 性能指标优秀
- [ ] 用户反馈良好

---

## 📝 开发注意事项

1. **代码规范**：遵循现有项目的 TypeScript 和 React 最佳实践
2. **组件复用**：充分利用现有的 shadcn/ui 组件库
3. **性能优先**：图片加载和数据获取的性能优化是重点
4. **移动端优先**：设计时先考虑移动端用户体验
5. **渐进增强**：核心功能先实现，再添加增强功能
6. **用户反馈**：在开发过程中收集和响应用户反馈

---

## 🔄 任务状态更新规则

- **未开始**: [ ] 
- **进行中**: [🔄]
- **已完成**: [✅]
- **已阻塞**: [🚫] (需要注明阻塞原因)
- **已跳过**: [⏭️] (需要注明跳过原因)