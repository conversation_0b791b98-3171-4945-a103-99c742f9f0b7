"use client";

import { useState, useCallback } from "react";
import type { NanoPrompt } from "@/types/nano-prompt";

interface UsePromptModalReturn {
  selectedPrompt: NanoPrompt | null;
  isOpen: boolean;
  openModal: (prompt: NanoPrompt) => void;
  closeModal: () => void;
  handlePromptClick: (prompt: NanoPrompt) => void;
  handleCopy: (prompt: NanoPrompt) => Promise<void>;
  handleShare: (prompt: NanoPrompt) => void;
}

interface UsePromptModalOptions {
  onCopy?: (prompt: NanoPrompt) => void;
  onShare?: (prompt: NanoPrompt) => void;
  onView?: (prompt: NanoPrompt) => void;
}

export function usePromptModal(options: UsePromptModalOptions = {}): UsePromptModalReturn {
  const { onCopy, onShare, onView } = options;
  
  const [selectedPrompt, setSelectedPrompt] = useState<NanoPrompt | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const openModal = useCallback((prompt: NanoPrompt) => {
    setSelectedPrompt(prompt);
    setIsOpen(true);
    
    // 通知外部有 prompt 被查看
    if (onView) {
      onView(prompt);
    }
  }, [onView]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    // 延迟清理选中的 prompt，给关闭动画时间
    setTimeout(() => setSelectedPrompt(null), 200);
  }, []);

  const handlePromptClick = useCallback((prompt: NanoPrompt) => {
    openModal(prompt);
  }, [openModal]);

  const handleCopy = useCallback(async (prompt: NanoPrompt) => {
    try {
      // 复制到剪贴板
      await navigator.clipboard.writeText(prompt.prompt_content);
      
      // 调用 API 记录复制行为
      try {
        const response = await fetch(`/api/prompts/${prompt.uuid}/copy`, {
          method: 'POST',
        });

        if (response.ok) {
          const data = await response.json();
          
          // 通知外部复制成功，传递新的复制次数
          if (onCopy) {
            onCopy({
              ...prompt,
              copy_count: data.newCount || prompt.copy_count + 1,
            });
          }
        } else {
          console.error('Failed to record copy action:', response.statusText);
          
          // 即使记录失败，也要通知外部复制成功
          if (onCopy) {
            onCopy(prompt);
          }
        }
      } catch (apiError) {
        console.error('Failed to record copy action:', apiError);
        
        // API 调用失败，但剪贴板复制成功，仍然通知外部
        if (onCopy) {
          onCopy(prompt);
        }
      }
      
    } catch (clipboardError) {
      console.error('Failed to copy to clipboard:', clipboardError);
      
      // 可以显示错误提示，但这里先简单处理
      throw new Error('Failed to copy prompt to clipboard');
    }
  }, [onCopy]);

  const handleShare = useCallback((prompt: NanoPrompt) => {
    if (navigator.share && navigator.canShare) {
      // 使用原生分享 API
      navigator.share({
        title: `Nano Banana Prompt: ${prompt.title}`,
        text: prompt.description || `Check out this amazing prompt: ${prompt.title}`,
        url: `${window.location.origin}/prompts/${prompt.uuid}`,
      }).catch((error) => {
        console.error('Native share failed:', error);
        // 降级到复制链接
        fallbackShare(prompt);
      });
    } else {
      // 降级到复制链接
      fallbackShare(prompt);
    }

    // 通知外部分享事件
    if (onShare) {
      onShare(prompt);
    }
  }, [onShare]);

  const fallbackShare = useCallback(async (prompt: NanoPrompt) => {
    try {
      const shareUrl = `${window.location.origin}/prompts/${prompt.uuid}`;
      await navigator.clipboard.writeText(shareUrl);
      
      // 这里可以显示一个 toast 提示链接已复制
      console.log('Share link copied to clipboard!');
      
    } catch (error) {
      console.error('Failed to copy share link:', error);
    }
  }, []);

  // 键盘事件处理
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' && isOpen) {
      closeModal();
    }
  }, [isOpen, closeModal]);

  // 监听键盘事件
  useState(() => {
    if (typeof window !== 'undefined') {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  });

  return {
    selectedPrompt,
    isOpen,
    openModal,
    closeModal,
    handlePromptClick,
    handleCopy,
    handleShare,
  };
}