"use client";

import { useState, useEffect, useCallback } from "react";
import type { 
  NanoPrompt, 
  PromptsQuery, 
  PromptsResponse, 
  PaginationInfo 
} from "@/types/nano-prompt";

interface UsePromptsState {
  prompts: NanoPrompt[];
  loading: boolean;
  error: string | null;
  pagination: PaginationInfo | null;
}

interface UsePromptsOptions {
  initialQuery?: PromptsQuery;
  autoFetch?: boolean;
}

interface UsePromptsReturn extends UsePromptsState {
  fetchPrompts: (query?: PromptsQuery) => Promise<void>;
  refetch: () => Promise<void>;
  loadMore: () => Promise<void>;
  updatePromptStats: (uuid: string, updates: { views?: number; copy_count?: number }) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  executeSearch: () => Promise<void>;
  hasMore: boolean;
  isLoadingMore: boolean;
}

export function usePrompts(options: UsePromptsOptions = {}): UsePromptsReturn {
  const { initialQuery = {}, autoFetch = false } = options;

  const [state, setState] = useState<UsePromptsState>({
    prompts: [],
    loading: false,
    error: null,
    pagination: null,
  });

  const [currentQuery, setCurrentQuery] = useState<PromptsQuery>(initialQuery);
  const [searchQuery, setSearchQuery] = useState(initialQuery.search || "");
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const fetchPrompts = useCallback(async (query: PromptsQuery = currentQuery) => {
    console.log('fetchPrompts called with query:', query);
    try {
      // 如果是新的搜索或筛选，重置为第一页
      const isNewQuery =
        query.search !== currentQuery.search ||
        query.category !== currentQuery.category ||
        query.author !== currentQuery.author;

      console.log('fetchPrompts: isNewQuery =', isNewQuery);

      if (isNewQuery) {
        setState(prev => ({ ...prev, loading: true, error: null }));
        query.page = 1;
      } else {
        setState(prev => ({ ...prev, loading: true, error: null }));
      }

      setCurrentQuery(query);

      // 构建查询参数
      const params = new URLSearchParams();
      if (query.page) params.append("page", query.page.toString());
      if (query.limit) params.append("limit", query.limit.toString());
      if (query.search) params.append("search", query.search);
      if (query.category) params.append("category", query.category);
      if (query.author) params.append("author", query.author);

      const response = await fetch(`/api/prompts?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: PromptsResponse = await response.json();

      setState(prev => ({
        ...prev,
        prompts: isNewQuery || query.page === 1 ? data.data : prev.prompts,
        loading: false,
        pagination: data.pagination,
        error: null,
      }));

    } catch (error) {
      console.error("Failed to fetch prompts:", error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Failed to fetch prompts",
      }));
    }
  }, [currentQuery]);

  const loadMore = useCallback(async () => {
    if (!state.pagination?.hasMore || state.loading || isLoadingMore) {
      return;
    }

    setIsLoadingMore(true);

    try {
      const nextPage = state.pagination.page + 1;
      const params = new URLSearchParams();
      params.append("page", nextPage.toString());
      if (currentQuery.limit) params.append("limit", currentQuery.limit.toString());
      if (currentQuery.search) params.append("search", currentQuery.search);
      if (currentQuery.category) params.append("category", currentQuery.category);
      if (currentQuery.author) params.append("author", currentQuery.author);

      const response = await fetch(`/api/prompts?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: PromptsResponse = await response.json();

      setState(prev => ({
        ...prev,
        prompts: [...prev.prompts, ...data.data],
        pagination: data.pagination,
      }));

      setCurrentQuery(prev => ({ ...prev, page: nextPage }));

    } catch (error) {
      console.error("Failed to load more prompts:", error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to load more prompts",
      }));
    } finally {
      setIsLoadingMore(false);
    }
  }, [state.pagination, state.loading, isLoadingMore, currentQuery]);

  const refetch = useCallback(() => {
    return fetchPrompts({ ...currentQuery, page: 1 });
  }, [fetchPrompts, currentQuery]);

  const updatePromptStats = useCallback((
    uuid: string,
    updates: { views?: number; copy_count?: number }
  ) => {
    setState(prev => ({
      ...prev,
      prompts: prev.prompts.map(prompt =>
        prompt.uuid === uuid
          ? {
              ...prompt,
              ...(updates.views !== undefined && { views: updates.views }),
              ...(updates.copy_count !== undefined && { copy_count: updates.copy_count }),
            }
          : prompt
      ),
    }));
  }, []);

  // 手动执行搜索
  const executeSearch = useCallback(async () => {
    await fetchPrompts({
      ...currentQuery,
      search: searchQuery || undefined,
      page: 1,
    });
  }, [fetchPrompts, currentQuery, searchQuery]);

  // 初始加载 - 只在组件挂载时执行一次
  useEffect(() => {
    console.log('usePrompts: Initial load effect triggered', { autoFetch, initialQuery });
    if (autoFetch) {
      console.log('usePrompts: Calling fetchPrompts with initialQuery', initialQuery);
      fetchPrompts(initialQuery);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoFetch]); // 只依赖 autoFetch，确保在挂载时执行

  return {
    ...state,
    fetchPrompts,
    refetch,
    loadMore,
    updatePromptStats,
    searchQuery,
    setSearchQuery,
    executeSearch,
    hasMore: state.pagination?.hasMore || false,
    isLoadingMore,
  };
}