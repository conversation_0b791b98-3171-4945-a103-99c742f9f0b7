import { nano_prompts } from "@/db/schema";
import { db } from "@/db";
import { and, desc, eq, ilike, sql } from "drizzle-orm";
import type { 
  NanoPrompt, 
  CreateNanoPrompt, 
  UpdateNanoPrompt, 
  PromptsQuery 
} from "@/types/nano-prompt";

export enum NanoPromptStatus {
  Online = "online",
  Offline = "offline",  
  Deleted = "deleted",
  Draft = "draft",
}

// 插入新的 nano prompt
export async function insertNanoPrompt(
  data: CreateNanoPrompt
): Promise<typeof nano_prompts.$inferSelect | undefined> {
  const [prompt] = await db()
    .insert(nano_prompts)
    .values({
      ...data,
      created_at: new Date(),
    })
    .returning();

  return prompt;
}

// 更新 nano prompt
export async function updateNanoPrompt(
  uuid: string,
  data: UpdateNanoPrompt
): Promise<typeof nano_prompts.$inferSelect | undefined> {
  const [prompt] = await db()
    .update(nano_prompts)
    .set({
      ...data,
      updated_at: new Date(),
    })
    .where(eq(nano_prompts.uuid, uuid))
    .returning();

  return prompt;
}

// 根据 UUID 查找单个 prompt
export async function findNanoPromptByUuid(
  uuid: string
): Promise<typeof nano_prompts.$inferSelect | undefined> {
  const [prompt] = await db()
    .select()
    .from(nano_prompts)
    .where(
      and(
        eq(nano_prompts.uuid, uuid),
        eq(nano_prompts.status, NanoPromptStatus.Online)
      )
    )
    .limit(1);

  return prompt;
}

// 获取所有 prompts (支持分页和搜索)
export async function getAllNanoPrompts(
  query: PromptsQuery = {}
): Promise<{
  data: (typeof nano_prompts.$inferSelect)[];
  total: number;
}> {
  const {
    page = 1,
    limit = 12,
    search,
    category,
    author,
  } = query;

  const offset = (page - 1) * limit;

  // 构建 where 条件
  const conditions = [eq(nano_prompts.status, NanoPromptStatus.Online)];

  if (search) {
    conditions.push(
      sql`(${ilike(nano_prompts.title, `%${search}%`)} OR ${ilike(nano_prompts.description, `%${search}%`)})`
    );
  }

  if (category) {
    conditions.push(eq(nano_prompts.category, category));
  }

  if (author) {
    conditions.push(eq(nano_prompts.author, author));
  }

  const whereCondition = conditions.length > 1 ? and(...conditions) : conditions[0];

  // 获取数据
  const data = await db()
    .select()
    .from(nano_prompts)
    .where(whereCondition)
    .orderBy(desc(nano_prompts.created_at))
    .limit(limit)
    .offset(offset);

  // 获取总数
  const [totalResult] = await db()
    .select({ count: sql<number>`count(*)` })
    .from(nano_prompts)
    .where(whereCondition);

  return {
    data,
    total: totalResult?.count || 0,
  };
}

// 按分类获取 prompts
export async function getNanoPromptsByCategory(
  category: string,
  page: number = 1,
  limit: number = 12
): Promise<{
  data: (typeof nano_prompts.$inferSelect)[];
  total: number;
}> {
  return getAllNanoPrompts({ page, limit, category });
}

// 获取热门 prompts (按浏览量排序)
export async function getPopularNanoPrompts(
  limit: number = 10
): Promise<(typeof nano_prompts.$inferSelect)[]> {
  const data = await db()
    .select()
    .from(nano_prompts)
    .where(eq(nano_prompts.status, NanoPromptStatus.Online))
    .orderBy(desc(nano_prompts.views))
    .limit(limit);

  return data;
}

// 增加浏览次数
export async function incrementViews(
  uuid: string
): Promise<{ success: boolean; newCount: number }> {
  try {
    const [result] = await db()
      .update(nano_prompts)
      .set({
        views: sql`${nano_prompts.views} + 1`,
        updated_at: new Date(),
      })
      .where(eq(nano_prompts.uuid, uuid))
      .returning({ views: nano_prompts.views });

    return {
      success: true,
      newCount: result?.views || 0,
    };
  } catch (error) {
    console.error("Failed to increment views:", error);
    return {
      success: false,
      newCount: 0,
    };
  }
}

// 增加复制次数
export async function incrementCopyCount(
  uuid: string
): Promise<{ success: boolean; newCount: number }> {
  try {
    const [result] = await db()
      .update(nano_prompts)
      .set({
        copy_count: sql`${nano_prompts.copy_count} + 1`,
        updated_at: new Date(),
      })
      .where(eq(nano_prompts.uuid, uuid))
      .returning({ copy_count: nano_prompts.copy_count });

    return {
      success: true,
      newCount: result?.copy_count || 0,
    };
  } catch (error) {
    console.error("Failed to increment copy count:", error);
    return {
      success: false,
      newCount: 0,
    };
  }
}

// 获取 prompts 总数
export async function getNanoPromptsTotal(): Promise<number> {
  const [result] = await db()
    .select({ count: sql<number>`count(*)` })
    .from(nano_prompts)
    .where(eq(nano_prompts.status, NanoPromptStatus.Online));

  return result?.count || 0;
}

// 删除 prompt (软删除)
export async function deleteNanoPrompt(
  uuid: string
): Promise<boolean> {
  try {
    await db()
      .update(nano_prompts)
      .set({
        status: NanoPromptStatus.Deleted,
        updated_at: new Date(),
      })
      .where(eq(nano_prompts.uuid, uuid));

    return true;
  } catch (error) {
    console.error("Failed to delete prompt:", error);
    return false;
  }
}