:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.3588 0.1354 278.6973);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3588 0.1354 278.6973);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3588 0.1354 278.6973);
  --primary: oklch(0.6056 0.2189 292.7172);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9618 0.0202 295.1913);
  --secondary-foreground: oklch(0.4568 0.2146 277.0229);
  --muted: oklch(0.9691 0.0161 293.7558);
  --muted-foreground: oklch(0.5413 0.2466 293.0090);
  --accent: oklch(0.9319 0.0316 255.5855);
  --accent-foreground: oklch(0.4244 0.1809 265.6377);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9299 0.0334 272.7879);
  --input: oklch(0.9299 0.0334 272.7879);
  --ring: oklch(0.6056 0.2189 292.7172);
  --chart-1: oklch(0.6056 0.2189 292.7172);
  --chart-2: oklch(0.5413 0.2466 293.0090);
  --chart-3: oklch(0.4907 0.2412 292.5809);
  --chart-4: oklch(0.4320 0.2106 292.7591);
  --chart-5: oklch(0.3796 0.1783 293.7446);
  --sidebar: oklch(0.9691 0.0161 293.7558);
  --sidebar-foreground: oklch(0.3588 0.1354 278.6973);
  --sidebar-primary: oklch(0.6056 0.2189 292.7172);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9319 0.0316 255.5855);
  --sidebar-accent-foreground: oklch(0.4244 0.1809 265.6377);
  --sidebar-border: oklch(0.9299 0.0334 272.7879);
  --sidebar-ring: oklch(0.6056 0.2189 292.7172);
  --font-sans: Roboto, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Fira Code, monospace;
  --radius: 0.625rem;
  --shadow-2xs: 2px 2px 4px 0px hsl(255 86% 66% / 0.10);
  --shadow-xs: 2px 2px 4px 0px hsl(255 86% 66% / 0.10);
  --shadow-sm: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 1px 2px -1px hsl(255 86% 66% / 0.20);
  --shadow: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 1px 2px -1px hsl(255 86% 66% / 0.20);
  --shadow-md: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 2px 4px -1px hsl(255 86% 66% / 0.20);
  --shadow-lg: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 4px 6px -1px hsl(255 86% 66% / 0.20);
  --shadow-xl: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 8px 10px -1px hsl(255 86% 66% / 0.20);
  --shadow-2xl: 2px 2px 4px 0px hsl(255 86% 66% / 0.50);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2077 0.0398 265.7549);
  --foreground: oklch(0.9299 0.0334 272.7879);
  --card: oklch(0.2573 0.0861 281.2883);
  --card-foreground: oklch(0.9299 0.0334 272.7879);
  --popover: oklch(0.2573 0.0861 281.2883);
  --popover-foreground: oklch(0.9299 0.0334 272.7879);
  --primary: oklch(0.6056 0.2189 292.7172);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.2573 0.0861 281.2883);
  --secondary-foreground: oklch(0.9299 0.0334 272.7879);
  --muted: oklch(0.2573 0.0861 281.2883);
  --muted-foreground: oklch(0.8112 0.1013 293.5712);
  --accent: oklch(0.4568 0.2146 277.0229);
  --accent-foreground: oklch(0.9299 0.0334 272.7879);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.2827 0.1351 291.0894);
  --input: oklch(0.2827 0.1351 291.0894);
  --ring: oklch(0.6056 0.2189 292.7172);
  --chart-1: oklch(0.7090 0.1592 293.5412);
  --chart-2: oklch(0.6056 0.2189 292.7172);
  --chart-3: oklch(0.5413 0.2466 293.0090);
  --chart-4: oklch(0.4907 0.2412 292.5809);
  --chart-5: oklch(0.4320 0.2106 292.7591);
  --sidebar: oklch(0.2077 0.0398 265.7549);
  --sidebar-foreground: oklch(0.9299 0.0334 272.7879);
  --sidebar-primary: oklch(0.6056 0.2189 292.7172);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.4568 0.2146 277.0229);
  --sidebar-accent-foreground: oklch(0.9299 0.0334 272.7879);
  --sidebar-border: oklch(0.2827 0.1351 291.0894);
  --sidebar-ring: oklch(0.6056 0.2189 292.7172);
  --font-sans: Roboto, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Fira Code, monospace;
  --radius: 0.625rem;
  --shadow-2xs: 2px 2px 4px 0px hsl(255 86% 66% / 0.10);
  --shadow-xs: 2px 2px 4px 0px hsl(255 86% 66% / 0.10);
  --shadow-sm: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 1px 2px -1px hsl(255 86% 66% / 0.20);
  --shadow: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 1px 2px -1px hsl(255 86% 66% / 0.20);
  --shadow-md: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 2px 4px -1px hsl(255 86% 66% / 0.20);
  --shadow-lg: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 4px 6px -1px hsl(255 86% 66% / 0.20);
  --shadow-xl: 2px 2px 4px 0px hsl(255 86% 66% / 0.20), 2px 8px 10px -1px hsl(255 86% 66% / 0.20);
  --shadow-2xl: 2px 2px 4px 0px hsl(255 86% 66% / 0.50);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}