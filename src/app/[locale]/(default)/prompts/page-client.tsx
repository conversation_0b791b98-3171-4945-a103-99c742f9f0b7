"use client";

import { useEffect } from "react";
import { Search, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { PromptCard } from "@/components/prompts/PromptCard";
import { PromptModal } from "@/components/prompts/PromptModal";
import { usePrompts } from "@/hooks/usePrompts";
import { usePromptModal } from "@/hooks/usePromptModal";

export function PromptsPageClient() {
  const {
    prompts,
    loading,
    error,
    searchQuery,
    setSearchQuery,
    hasMore,
    isLoadingMore,
    loadMore,
    updatePromptStats,
  } = usePrompts({
    initialQuery: { limit: 12 },
    autoFetch: true,
  });

  const {
    selectedPrompt,
    isOpen,
    handlePromptClick,
    closeModal,
    handleCopy,
    handleShare,
  } = usePromptModal({
    onCopy: (prompt) => {
      // 更新本地状态中的复制次数
      updatePromptStats(prompt.uuid, { copy_count: prompt.copy_count });
    },
  });

  const handleCardCopy = async (prompt: any) => {
    try {
      await handleCopy(prompt);
    } catch (error) {
      console.error("Copy failed:", error);
      // 这里可以显示错误提示
    }
  };

  // 无限滚动检测
  useEffect(() => {
    const handleScroll = () => {
      if (
        window.innerHeight + document.documentElement.scrollTop
        >= document.documentElement.offsetHeight - 1000 // 提前 1000px 开始加载
        && hasMore
        && !loading
        && !isLoadingMore
      ) {
        loadMore();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasMore, loading, isLoadingMore, loadMore]);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // 搜索已经通过 usePrompts 中的防抖处理自动执行
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* 页面标题 */}
      <div className="text-center mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold mb-3 sm:mb-4 px-4 sm:px-0">
          Discover Premium Nano Banana Prompts
        </h1>
        <p className="text-muted-foreground max-w-2xl mx-auto px-4 sm:px-0 text-sm sm:text-base">
          Browse expertly crafted AI prompts with before/after examples. 
          Find the perfect nano banana prompt for your image enhancement needs.
        </p>
      </div>

      {/* 搜索区域 */}
      <div className="mb-6 sm:mb-8 px-4 sm:px-0">
        <form onSubmit={handleSearchSubmit} className="max-w-md mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              type="text"
              placeholder="Search nano banana prompts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 pr-4 h-11 sm:h-10"
            />
          </div>
        </form>
      </div>

      {/* 结果统计 */}
      {!loading && (
        <div className="mb-6">
          <p className="text-sm text-muted-foreground text-center">
            💡 Showing {prompts.length} nano banana prompts
            {searchQuery && (
              <span> for "{searchQuery}"</span>
            )}
          </p>
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">
            <p className="text-lg font-medium">Oops! Something went wrong</p>
            <p className="text-sm">{error}</p>
          </div>
          <Button 
            variant="outline" 
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      )}

      {/* 加载状态 */}
      {loading && prompts.length === 0 && (
        <div className="text-center py-12">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Loading nano banana prompts...</p>
        </div>
      )}

      {/* 空状态 */}
      {!loading && prompts.length === 0 && !error && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-lg font-medium mb-2">No prompts found</h3>
          <p className="text-muted-foreground mb-4">
            {searchQuery 
              ? `No results for "${searchQuery}". Try different keywords.`
              : "No prompts available at the moment."
            }
          </p>
          {searchQuery && (
            <Button 
              variant="outline" 
              onClick={() => setSearchQuery("")}
            >
              Clear Search
            </Button>
          )}
        </div>
      )}

      {/* Prompts 网格 */}
      {prompts.length > 0 && (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
            {prompts.map((prompt, index) => (
              <PromptCard
                key={prompt.uuid}
                prompt={prompt}
                onClick={handlePromptClick}
                onCopy={handleCardCopy}
                priority={index < 6} // 前 6 个卡片优先加载图片
              />
            ))}
          </div>

          {/* 加载更多按钮 */}
          {hasMore && (
            <div className="text-center">
              <Button
                variant="outline"
                size="lg"
                onClick={loadMore}
                disabled={isLoadingMore}
                className="px-8"
              >
                {isLoadingMore ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    Loading...
                  </>
                ) : (
                  "Load More Prompts"
                )}
              </Button>
            </div>
          )}

          {/* 无限滚动加载指示器 */}
          {isLoadingMore && (
            <div className="text-center py-8">
              <Loader2 className="w-6 h-6 animate-spin mx-auto text-muted-foreground" />
            </div>
          )}

          {/* 已加载完所有内容 */}
          {!hasMore && prompts.length > 12 && (
            <div className="text-center py-8">
              <p className="text-sm text-muted-foreground">
                🎉 You've reached the end! All prompts loaded.
              </p>
            </div>
          )}
        </>
      )}

      {/* 详情模态框 */}
      <PromptModal
        prompt={selectedPrompt}
        isOpen={isOpen}
        onClose={closeModal}
        onCopy={handleCopy}
        onShare={handleShare}
      />
    </div>
  );
}