import type { Metadata } from "next";
import { PromptsPageClient } from "./page-client";

export const metadata: Metadata = {
  title: "Nano Banana Prompts - AI Image Enhancement Prompts",
  description: "Discover premium nano banana prompt collections for AI image enhancement. <PERSON><PERSON><PERSON> expertly crafted prompts with before/after examples for Gemini-2.5-Flash-Image-Preview and other AI models.",
  keywords: "nano banana prompts, AI image enhancement, photo editing prompts, AI prompts, image improvement, Gemini prompts",
  openGraph: {
    title: "Nano Banana Prompts - AI Image Enhancement Prompts",
    description: "Discover premium nano banana prompt collections for AI image enhancement. <PERSON><PERSON><PERSON> expertly crafted prompts with before/after examples.",
    type: "website",
    url: "/prompts",
  },
  alternates: {
    canonical: "/prompts",
  },
};

export default function PromptsPage() {
  return <PromptsPageClient />;
}