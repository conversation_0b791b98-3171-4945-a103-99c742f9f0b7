import { Loader2 } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

export default function PromptsLoading() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* 页面标题骨架 */}
      <div className="text-center mb-8">
        <Skeleton className="h-8 w-96 mx-auto mb-4" />
        <Skeleton className="h-4 w-[500px] mx-auto" />
      </div>

      {/* 搜索框骨架 */}
      <div className="mb-8">
        <div className="max-w-md mx-auto">
          <Skeleton className="h-10 w-full" />
        </div>
      </div>

      {/* 结果统计骨架 */}
      <div className="mb-6">
        <Skeleton className="h-4 w-48 mx-auto" />
      </div>

      {/* 加载指示器 */}
      <div className="text-center py-6 mb-8">
        <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-muted-foreground" />
        <p className="text-muted-foreground">Loading nano banana prompts...</p>
      </div>

      {/* Prompt 卡片骨架网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {Array.from({ length: 12 }, (_, index) => (
          <div key={index} className="border rounded-lg overflow-hidden">
            {/* 图片区域骨架 */}
            <Skeleton className="aspect-[2/1] w-full" />
            
            {/* 内容区域骨架 */}
            <div className="p-4 space-y-3">
              {/* 分类标签骨架 */}
              <Skeleton className="h-5 w-20" />
              
              {/* 标题骨架 */}
              <Skeleton className="h-6 w-full" />
              
              {/* 描述骨架 */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
              
              {/* 作者骨架 */}
              <Skeleton className="h-3 w-24" />
              
              {/* 底部操作栏骨架 */}
              <div className="flex items-center justify-between pt-2 border-t">
                <div className="flex gap-3">
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-4 w-12" />
                </div>
                <Skeleton className="h-8 w-16" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}