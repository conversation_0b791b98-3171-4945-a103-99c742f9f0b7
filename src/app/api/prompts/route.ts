import { NextRequest, NextResponse } from "next/server";
import { getAllNanoPrompts } from "@/models/nano-prompt";
import type { PromptsQuery, PromptsResponse, ErrorResponse } from "@/types/nano-prompt";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 解析查询参数
    const query: PromptsQuery = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: Math.min(parseInt(searchParams.get("limit") || "12"), 50), // 限制最大 50 个
      search: searchParams.get("search") || undefined,
      category: searchParams.get("category") || undefined,
      author: searchParams.get("author") || undefined,
    };

    // 验证参数
    if (query.page! < 1) {
      return NextResponse.json<ErrorResponse>(
        {
          error: "Invalid page parameter",
          message: "Page must be greater than 0",
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    if (query.limit! < 1 || query.limit! > 50) {
      return NextResponse.json<ErrorResponse>(
        {
          error: "Invalid limit parameter", 
          message: "Limit must be between 1 and 50",
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    // 获取数据
    const { data, total } = await getAllNanoPrompts(query);

    // 计算分页信息
    const page = query.page!;
    const limit = query.limit!;
    const totalPages = Math.ceil(total / limit);
    const hasMore = page < totalPages;

    const response: PromptsResponse = {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasMore,
      },
    };

    // 设置缓存头 (5分钟)
    return NextResponse.json(response, {
      headers: {
        "Cache-Control": "public, s-maxage=300, stale-while-revalidate=3600",
      },
    });
  } catch (error) {
    console.error("Error fetching prompts:", error);
    
    return NextResponse.json<ErrorResponse>(
      {
        error: "Internal server error",
        message: "Failed to fetch prompts",
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}