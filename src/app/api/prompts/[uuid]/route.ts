import { NextRequest, NextResponse } from "next/server";
import { findNanoPromptByUuid, incrementViews } from "@/models/nano-prompt";
import type { PromptResponse, ErrorResponse } from "@/types/nano-prompt";

interface RouteParams {
  params: {
    uuid: string;
  };
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { uuid } = params;

    // 验证 UUID 格式
    if (!uuid || typeof uuid !== "string") {
      return NextResponse.json<ErrorResponse>(
        {
          error: "Invalid UUID",
          message: "UUID parameter is required",
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    // 查找 prompt
    const prompt = await findNanoPromptByUuid(uuid);

    if (!prompt) {
      return NextResponse.json<ErrorResponse>(
        {
          error: "Prompt not found",
          message: `Prompt with UUID ${uuid} not found or not available`,
          statusCode: 404,
        },
        { status: 404 }
      );
    }

    // 自动增加浏览次数 (异步执行，不等待结果)
    incrementViews(uuid).catch((error) => {
      console.error("Failed to increment views:", error);
    });

    const response: PromptResponse = {
      data: prompt,
    };

    // 设置缓存头 (2分钟，因为包含浏览次数统计)
    return NextResponse.json(response, {
      headers: {
        "Cache-Control": "public, s-maxage=120, stale-while-revalidate=1800",
      },
    });
  } catch (error) {
    console.error("Error fetching prompt:", error);
    
    return NextResponse.json<ErrorResponse>(
      {
        error: "Internal server error", 
        message: "Failed to fetch prompt details",
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}