import { NextRequest, NextResponse } from "next/server";
import { incrementCopyCount, findNanoPromptByUuid } from "@/models/nano-prompt";
import type { StatsUpdateResponse, ErrorResponse } from "@/types/nano-prompt";

interface RouteParams {
  params: {
    uuid: string;
  };
}

export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { uuid } = params;

    // 验证 UUID 格式
    if (!uuid || typeof uuid !== "string") {
      return NextResponse.json<ErrorResponse>(
        {
          error: "Invalid UUID",
          message: "UUID parameter is required",
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    // 验证 prompt 是否存在
    const prompt = await findNanoPromptByUuid(uuid);
    if (!prompt) {
      return NextResponse.json<ErrorResponse>(
        {
          error: "Prompt not found",
          message: `Prompt with UUID ${uuid} not found or not available`,
          statusCode: 404,
        },
        { status: 404 }
      );
    }

    // 增加复制次数
    const result = await incrementCopyCount(uuid);

    if (!result.success) {
      return NextResponse.json<ErrorResponse>(
        {
          error: "Failed to update copy count",
          message: "Unable to record copy action",
          statusCode: 500,
        },
        { status: 500 }
      );
    }

    const response: StatsUpdateResponse = {
      success: true,
      newCount: result.newCount,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating copy count:", error);
    
    return NextResponse.json<ErrorResponse>(
      {
        error: "Internal server error",
        message: "Failed to record copy action", 
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}

// 可选：支持 GET 方法获取当前复制次数
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { uuid } = params;

    if (!uuid || typeof uuid !== "string") {
      return NextResponse.json<ErrorResponse>(
        {
          error: "Invalid UUID", 
          message: "UUID parameter is required",
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    const prompt = await findNanoPromptByUuid(uuid);
    
    if (!prompt) {
      return NextResponse.json<ErrorResponse>(
        {
          error: "Prompt not found",
          message: `Prompt with UUID ${uuid} not found or not available`,
          statusCode: 404,
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      copy_count: prompt.copy_count,
      views: prompt.views,
    });
  } catch (error) {
    console.error("Error fetching copy stats:", error);
    
    return NextResponse.json<ErrorResponse>(
      {
        error: "Internal server error",
        message: "Failed to fetch copy statistics",
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}