import { NextRequest, NextResponse } from "next/server";
import { newStorage } from "@/lib/storage";
import { insertNanoPrompt } from "@/models/nano-prompt";
import { 
  validateImageFile, 
  generateImagePath, 
  fileToBuffer, 
  getContentType 
} from "@/lib/image-utils";
import type { ErrorResponse } from "@/types/nano-prompt";

interface UploadResponse {
  success: boolean;
  data?: {
    uuid: string;
    title: string;
    before_image_url: string;
    after_image_url: string;
  };
  error?: string;
  details?: string[];
}

export async function POST(request: NextRequest) {
  try {
    // 解析 FormData
    const formData = await request.formData();
    
    // 提取文本字段
    const title = formData.get('title') as string;
    const description = formData.get('description') as string || null;
    const category = formData.get('category') as string || null;
    const author = formData.get('author') as string || null;
    const prompt_content = formData.get('prompt_content') as string;
    
    // 验证必填字段
    if (!title?.trim()) {
      return NextResponse.json<UploadResponse>(
        {
          success: false,
          error: "Title is required"
        },
        { status: 400 }
      );
    }
    
    if (!prompt_content?.trim()) {
      return NextResponse.json<UploadResponse>(
        {
          success: false,
          error: "Prompt content is required"
        },
        { status: 400 }
      );
    }
    
    // 提取图片文件
    const beforeImage = formData.get('before_image') as File | null;
    const afterImage = formData.get('after_image') as File | null;
    
    if (!beforeImage) {
      return NextResponse.json<UploadResponse>(
        {
          success: false,
          error: "Before image is required"
        },
        { status: 400 }
      );
    }
    
    if (!afterImage) {
      return NextResponse.json<UploadResponse>(
        {
          success: false,
          error: "After image is required"
        },
        { status: 400 }
      );
    }
    
    // 验证图片文件
    const beforeValidation = validateImageFile(beforeImage);
    const afterValidation = validateImageFile(afterImage);
    
    const validationErrors: string[] = [];
    if (!beforeValidation.valid && beforeValidation.error) {
      validationErrors.push(`Before image: ${beforeValidation.error}`);
    }
    if (!afterValidation.valid && afterValidation.error) {
      validationErrors.push(`After image: ${afterValidation.error}`);
    }
    
    if (validationErrors.length > 0) {
      return NextResponse.json<UploadResponse>(
        {
          success: false,
          error: "Image validation failed",
          details: validationErrors
        },
        { status: 400 }
      );
    }
    
    // 生成 UUID
    const uuid = crypto.randomUUID();
    
    // 生成存储路径
    const beforePath = generateImagePath(uuid, 'before', beforeImage.name);
    const afterPath = generateImagePath(uuid, 'after', afterImage.name);
    
    // 初始化存储客户端
    const storage = newStorage();
    
    try {
      // 转换文件为 Buffer
      const beforeBuffer = await fileToBuffer(beforeImage);
      const afterBuffer = await fileToBuffer(afterImage);
      
      // 上传图片到 R2
      const [beforeUploadResult, afterUploadResult] = await Promise.all([
        storage.uploadFile({
          body: beforeBuffer,
          key: beforePath,
          contentType: getContentType(beforeImage.name),
          disposition: "inline"
        }),
        storage.uploadFile({
          body: afterBuffer,
          key: afterPath,
          contentType: getContentType(afterImage.name),
          disposition: "inline"
        })
      ]);
      
      console.log('Images uploaded successfully:', {
        before: beforeUploadResult.key,
        after: afterUploadResult.key
      });
      
    } catch (uploadError) {
      console.error('Failed to upload images to R2:', uploadError);
      return NextResponse.json<UploadResponse>(
        {
          success: false,
          error: "Failed to upload images to storage",
          details: [uploadError instanceof Error ? uploadError.message : 'Unknown upload error']
        },
        { status: 500 }
      );
    }
    
    // 保存到数据库
    try {
      const prompt = await insertNanoPrompt({
        uuid,
        title: title.trim(),
        description: description?.trim() || null,
        category: category?.trim() || null,
        author: author?.trim() || null,
        prompt_content: prompt_content.trim(),
        before_image_url: `${process.env.STORAGE_DOMAIN}/${beforePath}`,
        after_image_url: `${process.env.STORAGE_DOMAIN}/${afterPath}`,
        status: 'online'
      });
      
      if (!prompt) {
        throw new Error('Failed to create prompt record');
      }
      
      console.log('Prompt created successfully:', prompt.uuid);
      
      return NextResponse.json<UploadResponse>({
        success: true,
        data: {
          uuid: prompt.uuid,
          title: prompt.title,
          before_image_url: beforePath,
          after_image_url: afterPath
        }
      });
      
    } catch (dbError) {
      console.error('Failed to save prompt to database:', dbError);
      
      // 数据库保存失败，尝试清理已上传的文件
      // (这里可以添加清理逻辑，但 R2 没有简单的删除方法，先记录错误)
      console.warn('Images were uploaded but database save failed. Manual cleanup may be needed:', {
        beforePath,
        afterPath
      });
      
      return NextResponse.json<UploadResponse>(
        {
          success: false,
          error: "Failed to save prompt to database",
          details: [dbError instanceof Error ? dbError.message : 'Unknown database error']
        },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Upload request processing error:', error);
    
    return NextResponse.json<UploadResponse>(
      {
        success: false,
        error: "Internal server error",
        details: [error instanceof Error ? error.message : 'Unknown error']
      },
      { status: 500 }
    );
  }
}

// 可选：支持 GET 方法返回上传表单信息
export async function GET() {
  return NextResponse.json({
    message: "Prompt upload endpoint",
    method: "POST",
    contentType: "multipart/form-data",
    requiredFields: ["title", "prompt_content", "before_image", "after_image"],
    optionalFields: ["description", "category", "author"],
    maxFileSize: "10MB",
    allowedImageTypes: ["image/jpeg", "image/png", "image/webp", "image/gif"]
  });
}