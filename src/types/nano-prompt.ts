// Nano Banana Prompts 相关类型定义

export interface ExampleImage {
  before: string;
  after: string;
  title?: string;
  description?: string;
}

export interface NanoPrompt {
  id: number;
  uuid: string;
  title: string;
  category: string | null;
  author: string | null;
  description: string | null;
  prompt_content: string;
  before_image_url: string | null;
  after_image_url: string | null;
  views: number;
  copy_count: number;
  status: string;
  created_at: string;
  updated_at: string | null;
}

// 用于插入新 prompt 的类型
export interface CreateNanoPrompt {
  uuid: string;
  title: string;
  category?: string | null;
  author?: string | null;
  description?: string | null;
  prompt_content: string;
  before_image_url?: string | null;
  after_image_url?: string | null;
  status?: string;
}

// 用于更新 prompt 的类型
export interface UpdateNanoPrompt {
  title?: string;
  category?: string | null;
  author?: string | null;
  description?: string | null;
  prompt_content?: string;
  before_image_url?: string | null;
  after_image_url?: string | null;
  status?: string;
  updated_at?: string;
}

// API 查询参数
export interface PromptsQuery {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  author?: string;
  locale?: string;
}

// 分页信息
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  hasMore: boolean;
  totalPages: number;
}

// API 响应类型
export interface PromptsResponse {
  data: NanoPrompt[];
  pagination: PaginationInfo;
}

// 单个 prompt 响应
export interface PromptResponse {
  data: NanoPrompt;
}

// 统计更新响应
export interface StatsUpdateResponse {
  success: boolean;
  newCount: number;
}

// Prompt 状态枚举
export enum PromptStatus {
  Online = 'online',
  Offline = 'offline',
  Deleted = 'deleted',
  Draft = 'draft'
}

// 错误响应类型
export interface ErrorResponse {
  error: string;
  message: string;
  statusCode: number;
}