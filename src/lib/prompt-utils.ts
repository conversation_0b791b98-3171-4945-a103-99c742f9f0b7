import type { NanoPrompt } from "@/types/nano-prompt";

/**
 * 复制文本到剪贴板
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      // 使用现代 Clipboard API
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 降级到传统方法
      return fallbackCopyToClipboard(text);
    }
  } catch (error) {
    console.error("Failed to copy to clipboard:", error);
    return fallbackCopyToClipboard(text);
  }
}

/**
 * 降级的复制方法 (适用于不支持 Clipboard API 的浏览器)
 */
function fallbackCopyToClipboard(text: string): boolean {
  try {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    
    // 避免滚动到底部
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    const result = document.execCommand("copy");
    document.body.removeChild(textArea);
    
    return result;
  } catch (error) {
    console.error("Fallback copy failed:", error);
    return false;
  }
}

/**
 * 格式化数字显示 (1000 -> 1k, 1000000 -> 1M)
 */
export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}k`;
  }
  return num.toString();
}

/**
 * 截断文本
 */
export function truncateText(text: string | null | undefined, maxLength: number): string {
  if (!text) return "";
  if (text.length <= maxLength) return text;
  return `${text.substring(0, maxLength).trim()}...`;
}

/**
 * 格式化日期显示
 */
export function formatDate(dateString: string | null | undefined, locale: string = "en-US"): string {
  if (!dateString) return "";
  
  try {
    const date = new Date(dateString);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) return "";
    
    // 如果是今天，显示 "Today"
    const today = new Date();
    if (date.toDateString() === today.toDateString()) {
      return "Today";
    }
    
    // 如果是昨天，显示 "Yesterday"
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    }
    
    // 如果是本周内，显示星期
    const weekAgo = new Date(today);
    weekAgo.setDate(weekAgo.getDate() - 7);
    if (date > weekAgo) {
      return date.toLocaleDateString(locale, { weekday: 'long' });
    }
    
    // 否则显示完整日期
    return date.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    console.error("Date formatting failed:", error);
    return "";
  }
}

/**
 * 格式化相对时间 (5 minutes ago, 2 hours ago, etc.)
 */
export function formatRelativeTime(dateString: string | null | undefined): string {
  if (!dateString) return "";
  
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return "Just now";
    }
    
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
    }
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
    }
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) {
      return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
    }
    
    return formatDate(dateString);
  } catch (error) {
    console.error("Relative time formatting failed:", error);
    return "";
  }
}

/**
 * 生成分享链接
 */
export function generateShareUrl(prompt: NanoPrompt, baseUrl?: string): string {
  const base = baseUrl || (typeof window !== 'undefined' ? window.location.origin : '');
  return `${base}/prompts?uuid=${prompt.uuid}`;
}

/**
 * 生成分享文本
 */
export function generateShareText(prompt: NanoPrompt): string {
  const title = prompt.title;
  const description = prompt.description ? ` - ${truncateText(prompt.description, 100)}` : '';
  return `Check out this amazing Nano Banana Prompt: ${title}${description}`;
}

/**
 * 分享 prompt (使用原生分享或降级到复制链接)
 */
export async function sharePrompt(prompt: NanoPrompt): Promise<{ success: boolean; method: 'native' | 'clipboard' | 'fallback' }> {
  const shareUrl = generateShareUrl(prompt);
  const shareText = generateShareText(prompt);
  
  // 尝试使用原生分享 API
  if (navigator.share && navigator.canShare) {
    try {
      await navigator.share({
        title: `Nano Banana Prompt: ${prompt.title}`,
        text: shareText,
        url: shareUrl,
      });
      return { success: true, method: 'native' };
    } catch (error) {
      // 用户取消分享或分享失败，降级到复制链接
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Native share failed:', error);
      }
    }
  }
  
  // 降级到复制链接
  try {
    const copyText = `${shareText}\n${shareUrl}`;
    const success = await copyToClipboard(copyText);
    return { 
      success, 
      method: success ? 'clipboard' : 'fallback' 
    };
  } catch (error) {
    console.error('Share fallback failed:', error);
    return { success: false, method: 'fallback' };
  }
}

/**
 * 高亮搜索关键词
 */
export function highlightSearchTerm(text: string, searchTerm: string): string {
  if (!searchTerm.trim()) return text;
  
  try {
    const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  } catch (error) {
    console.error('Highlight failed:', error);
    return text;
  }
}

/**
 * 转义正则表达式特殊字符
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * 验证图片 URL
 */
export function isValidImageUrl(url: string | null | undefined): boolean {
  if (!url) return false;
  
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname.toLowerCase();
    
    // 检查文件扩展名
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'];
    return imageExtensions.some(ext => pathname.endsWith(ext));
  } catch (error) {
    return false;
  }
}

/**
 * 获取图片占位符 URL (用于图片加载失败时)
 */
export function getImagePlaceholder(width: number = 400, height: number = 300): string {
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f3f4f6"/>
      <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af" font-family="Arial, sans-serif" font-size="14">
        Image not available
      </text>
    </svg>
  `.trim())}`;
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
}