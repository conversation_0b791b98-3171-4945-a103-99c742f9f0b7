/**
 * 图片处理和验证工具函数
 * 配合现有的 Storage 类使用
 */

// 允许的图片类型
const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp',
  'image/gif'
];

// 允许的文件扩展名
const ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'webp', 'gif'];

// 最大文件大小 (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

/**
 * 验证图片文件
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // 检查文件类型
  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
    return {
      valid: false,
      error: `Invalid file type. Allowed types: ${ALLOWED_IMAGE_TYPES.join(', ')}`
    };
  }

  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `File size too large. Maximum size: ${formatFileSize(MAX_FILE_SIZE)}`
    };
  }

  // 检查文件扩展名
  const extension = getFileExtension(file.name);
  if (!ALLOWED_EXTENSIONS.includes(extension)) {
    return {
      valid: false,
      error: `Invalid file extension. Allowed extensions: ${ALLOWED_EXTENSIONS.join(', ')}`
    };
  }

  return { valid: true };
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
}

/**
 * 生成图片存储路径
 */
export function generateImagePath(uuid: string, type: 'before' | 'after', filename: string): string {
  const extension = getFileExtension(filename);
  return `prompts/${type}/${uuid}.${extension}`;
}

/**
 * 从路径中提取文件信息
 */
export function parseImagePath(path: string): {
  type: 'before' | 'after';
  uuid: string;
  extension: string;
} | null {
  const match = path.match(/^prompts\/(before|after)\/([^.]+)\.(.+)$/);
  if (!match) return null;
  
  return {
    type: match[1] as 'before' | 'after',
    uuid: match[2],
    extension: match[3]
  };
}

/**
 * 将 File 转换为 Buffer
 */
export async function fileToBuffer(file: File): Promise<Buffer> {
  const arrayBuffer = await file.arrayBuffer();
  return Buffer.from(arrayBuffer);
}

/**
 * 获取文件的 MIME 类型
 */
export function getContentType(filename: string): string {
  const extension = getFileExtension(filename);
  
  switch (extension) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'webp':
      return 'image/webp';
    case 'gif':
      return 'image/gif';
    default:
      return 'image/jpeg'; // 默认
  }
}

/**
 * 格式化文件大小显示
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 生成唯一的文件名
 */
export function generateUniqueFilename(originalFilename: string): string {
  const extension = getFileExtension(originalFilename);
  const uuid = crypto.randomUUID();
  return `${uuid}.${extension}`;
}

/**
 * 检查文件是否为图片
 */
export function isImageFile(file: File): boolean {
  return ALLOWED_IMAGE_TYPES.includes(file.type);
}

/**
 * 生成图片预览 URL (用于表单预览)
 */
export function createImagePreviewUrl(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * 清理预览 URL (避免内存泄漏)
 */
export function revokeImagePreviewUrl(url: string): void {
  URL.revokeObjectURL(url);
}

/**
 * 批量验证多个图片文件
 */
export function validateImageFiles(files: File[]): {
  valid: boolean;
  errors: string[];
  validFiles: File[];
  invalidFiles: File[];
} {
  const errors: string[] = [];
  const validFiles: File[] = [];
  const invalidFiles: File[] = [];

  for (const file of files) {
    const validation = validateImageFile(file);
    if (validation.valid) {
      validFiles.push(file);
    } else {
      invalidFiles.push(file);
      if (validation.error) {
        errors.push(`${file.name}: ${validation.error}`);
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    validFiles,
    invalidFiles
  };
}

/**
 * 从存储路径生成显示用的图片 URL
 */
export function getImageDisplayUrl(imagePath: string | null): string | null {
  if (!imagePath) return null;
  
  const baseUrl = process.env.STORAGE_DOMAIN || process.env.NEXT_PUBLIC_STORAGE_DOMAIN;
  if (!baseUrl) {
    console.warn('STORAGE_DOMAIN is not configured');
    return null;
  }
  
  return `${baseUrl}/${imagePath}`;
}

/**
 * 图片上传进度处理器类型
 */
export type UploadProgressHandler = (progress: number) => void;

/**
 * 图片上传配置类型
 */
export interface ImageUploadConfig {
  onProgress?: UploadProgressHandler;
  maxFileSize?: number;
  allowedTypes?: string[];
}