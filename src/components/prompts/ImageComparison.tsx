"use client";

import Image from "next/image";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface ImageComparisonProps {
  beforeImage: string | null;
  afterImage: string | null;
  title?: string;
  className?: string;
  priority?: boolean;
}

export function ImageComparison({
  beforeImage,
  afterImage,
  title = "Image Comparison",
  className,
  priority = false,
}: ImageComparisonProps) {
  const [beforeLoading, setBeforeLoading] = useState(true);
  const [afterLoading, setAfterLoading] = useState(true);
  const [beforeError, setBeforeError] = useState(false);
  const [afterError, setAfterError] = useState(false);

  const hasImages = beforeImage || afterImage;

  if (!hasImages) {
    return (
      <div className={cn("relative aspect-[2/1] bg-muted rounded-lg", className)}>
        <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
          <div className="text-center">
            <div className="text-2xl mb-2">🖼️</div>
            <p className="text-sm">No images available</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("relative aspect-[2/1] bg-muted rounded-lg overflow-hidden", className)}>
      <div className="grid grid-cols-2 h-full">
        {/* Before Image */}
        <div className="relative border-r border-border/50">
          {beforeImage ? (
            <>
              <Image
                src={beforeImage}
                alt={`Before - ${title}`}
                fill
                className={cn(
                  "object-cover transition-opacity duration-300",
                  beforeLoading && "opacity-0"
                )}
                priority={priority}
                sizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 20vw"
                onLoad={() => setBeforeLoading(false)}
                onError={() => {
                  setBeforeError(true);
                  setBeforeLoading(false);
                }}
              />
              
              {beforeLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-muted">
                  <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary border-t-transparent" />
                </div>
              )}
              
              {beforeError && (
                <div className="absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground">
                  <div className="text-center">
                    <div className="text-xl mb-1">📸</div>
                    <p className="text-xs">Failed to load</p>
                  </div>
                </div>
              )}
              
              {/* Before Label */}
              <div className="absolute top-2 left-2 px-2 py-1 bg-black/70 text-white text-xs rounded-md font-medium">
                BEFORE
              </div>
            </>
          ) : (
            <div className="absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground">
              <div className="text-center">
                <div className="text-xl mb-1">📸</div>
                <p className="text-xs">Original</p>
              </div>
            </div>
          )}
        </div>

        {/* After Image */}
        <div className="relative">
          {afterImage ? (
            <>
              <Image
                src={afterImage}
                alt={`After - ${title}`}
                fill
                className={cn(
                  "object-cover transition-opacity duration-300",
                  afterLoading && "opacity-0"
                )}
                priority={priority}
                sizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 20vw"
                onLoad={() => setAfterLoading(false)}
                onError={() => {
                  setAfterError(true);
                  setAfterLoading(false);
                }}
              />
              
              {afterLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-muted">
                  <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary border-t-transparent" />
                </div>
              )}
              
              {afterError && (
                <div className="absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground">
                  <div className="text-center">
                    <div className="text-xl mb-1">✨</div>
                    <p className="text-xs">Failed to load</p>
                  </div>
                </div>
              )}
              
              {/* After Label */}
              <div className="absolute top-2 right-2 px-2 py-1 bg-primary text-primary-foreground text-xs rounded-md font-medium">
                AFTER
              </div>
            </>
          ) : (
            <div className="absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground">
              <div className="text-center">
                <div className="text-xl mb-1">✨</div>
                <p className="text-xs">Enhanced</p>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Arrow Indicator */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-background border border-border rounded-full p-2 shadow-md">
        <svg
          className="w-4 h-4 text-muted-foreground"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 7l5 5m0 0l-5 5m5-5H6"
          />
        </svg>
      </div>
    </div>
  );
}