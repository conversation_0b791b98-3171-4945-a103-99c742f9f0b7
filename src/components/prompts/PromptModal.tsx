"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, X, Share2 } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { ImageComparison } from "./ImageComparison";
import { cn } from "@/lib/utils";
import type { NanoPrompt } from "@/types/nano-prompt";

interface PromptModalProps {
  prompt: NanoPrompt | null;
  isOpen: boolean;
  onClose: () => void;
  onCopy?: (prompt: NanoPrompt) => void;
  onShare?: (prompt: NanoPrompt) => void;
}

export function PromptModal({
  prompt,
  isOpen,
  onClose,
  onCopy,
  onShare,
}: PromptModalProps) {
  const [copying, setCopying] = useState(false);

  if (!prompt) return null;

  const handleCopyClick = async () => {
    if (copying) return;
    
    setCopying(true);
    
    try {
      await navigator.clipboard.writeText(prompt.prompt_content);
      
      // 调用回调函数通知复制事件
      if (onCopy) {
        onCopy(prompt);
      }
      
      // 简单的成功反馈 (后续可以用 toast)
      console.log("Full prompt copied to clipboard!");
    } catch (error) {
      console.error("Failed to copy prompt:", error);
    } finally {
      setTimeout(() => setCopying(false), 1000);
    }
  };

  const handleShareClick = () => {
    if (onShare) {
      onShare(prompt);
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  const formatDate = (dateString: string): string => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return '';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] w-[95vw] sm:w-full p-0">
        <DialogHeader className="p-4 sm:p-6 pb-3 sm:pb-4">
          <div className="flex items-start justify-between">
            <div className="flex-1 pr-2 sm:pr-4">
              <DialogTitle className="text-lg sm:text-xl font-semibold leading-tight mb-2">
                {prompt.title}
              </DialogTitle>
              
              <div className="flex items-center gap-2 sm:gap-4 text-xs sm:text-sm text-muted-foreground flex-wrap">
                <div className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  <span>{formatNumber(prompt.views)} views</span>
                </div>
                
                <div className="flex items-center gap-1">
                  <Copy className="w-4 h-4" />
                  <span>{formatNumber(prompt.copy_count)} copies</span>
                </div>
                
                {prompt.author && (
                  <span>by {prompt.author}</span>
                )}
                
                {prompt.created_at && (
                  <span>{formatDate(prompt.created_at)}</span>
                )}
              </div>
            </div>
          </div>
        </DialogHeader>

        <ScrollArea className="flex-1">
          <div className="px-4 sm:px-6 pb-4 sm:pb-6 space-y-4 sm:space-y-6">
            {/* 分类标签 */}
            {prompt.category && (
              <div className="flex gap-2">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                  {prompt.category}
                </span>
              </div>
            )}

            {/* 图片展示区域 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Example Results</h3>
              
              <ImageComparison
                beforeImage={prompt.before_image_url}
                afterImage={prompt.after_image_url}
                title={prompt.title}
                className="aspect-[2/1]"
                priority
              />
            </div>

            {/* 描述 */}
            {prompt.description && (
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Description</h3>
                <p className="text-muted-foreground leading-relaxed">
                  {prompt.description}
                </p>
              </div>
            )}

            <Separator />

            {/* Prompt 内容 */}
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <h3 className="text-base sm:text-lg font-medium">Complete Nano Banana Prompt</h3>
                <div className="flex gap-2 flex-col sm:flex-row">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleShareClick}
                    className="flex-1 sm:flex-none"
                  >
                    <Share2 className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                  
                  <Button
                    className={cn(
                      "transition-all flex-1 sm:flex-none",
                      copying && "bg-green-600 hover:bg-green-700"
                    )}
                    size="sm"
                    onClick={handleCopyClick}
                    disabled={copying}
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    {copying ? "Copied!" : "Copy Full Prompt"}
                  </Button>
                </div>
              </div>
              
              <div className="relative">
                <div className="bg-muted/50 border rounded-lg p-4">
                  <pre className="text-sm leading-relaxed whitespace-pre-wrap font-mono">
                    {prompt.prompt_content}
                  </pre>
                </div>
                
                {/* 复制成功反馈 */}
                {copying && (
                  <div className="absolute top-2 right-2 bg-green-600 text-white text-xs px-2 py-1 rounded-md">
                    Copied!
                  </div>
                )}
              </div>
            </div>

            {/* 使用提示 (如果有的话，预留) */}
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Pro Tips</h3>
              <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <li>• Works best with high-resolution source images</li>
                  <li>• Ideal for photos that need color and lighting enhancement</li>
                  <li>• Use with Gemini-2.5-Flash-Image-Preview for best results</li>
                </ul>
              </div>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}