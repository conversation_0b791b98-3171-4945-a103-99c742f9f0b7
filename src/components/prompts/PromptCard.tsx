"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ImageComparison } from "./ImageComparison";
import { cn } from "@/lib/utils";
import type { NanoPrompt } from "@/types/nano-prompt";

interface PromptCardProps {
  prompt: NanoPrompt;
  onCopy?: (prompt: NanoPrompt) => void;
  onClick?: (prompt: NanoPrompt) => void;
  className?: string;
  priority?: boolean;
}

export function PromptCard({
  prompt,
  onCopy,
  onClick,
  className,
  priority = false,
}: PromptCardProps) {
  const [copying, setCopying] = useState(false);

  const handleCopyClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (copying) return;
    
    setCopying(true);
    
    try {
      await navigator.clipboard.writeText(prompt.prompt_content);
      
      // 调用回调函数通知复制事件
      if (onCopy) {
        onCopy(prompt);
      }
      
      // 简单的成功反馈 (后续可以用 toast)
      console.log("Prompt copied to clipboard!");
    } catch (error) {
      console.error("Failed to copy prompt:", error);
    } finally {
      setTimeout(() => setCopying(false), 500);
    }
  };

  const handleCardClick = () => {
    if (onClick) {
      onClick(prompt);
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  const truncateText = (text: string | null, maxLength: number): string => {
    if (!text) return "";
    if (text.length <= maxLength) return text;
    return `${text.substring(0, maxLength)}...`;
  };

  return (
    <Card 
      className={cn(
        "group cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1",
        "border border-border hover:border-primary/20",
        className
      )}
      onClick={handleCardClick}
    >
      <CardContent className="p-0">
        {/* 图片对比区域 */}
        <div className="relative">
          <ImageComparison
            beforeImage={prompt.before_image_url}
            afterImage={prompt.after_image_url}
            title={prompt.title}
            className="rounded-t-lg border-0"
            priority={priority}
          />
          
          {/* 悬停时的遮罩 */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 rounded-t-lg" />
        </div>

        {/* 内容区域 */}
        <div className="p-4 space-y-3">
          {/* 标题和分类 */}
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              {/* Emoji 如果有的话 */}
              {prompt.category && (
                <span className="text-xs px-2 py-1 bg-muted text-muted-foreground rounded-md font-medium">
                  {prompt.category}
                </span>
              )}
            </div>
            
            <h3 className="font-semibold text-base leading-tight group-hover:text-primary transition-colors">
              {truncateText(prompt.title, 50)}
            </h3>
          </div>

          {/* 描述 */}
          {prompt.description && (
            <p className="text-sm text-muted-foreground leading-relaxed">
              {truncateText(prompt.description, 80)}
            </p>
          )}

          {/* 作者信息 */}
          {prompt.author && (
            <p className="text-xs text-muted-foreground">
              by {prompt.author}
            </p>
          )}

          {/* 底部操作栏 */}
          <div className="flex items-center justify-between pt-2 border-t border-border/50">
            {/* 统计信息 */}
            <div className="flex items-center gap-2 sm:gap-3 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Eye className="w-3 h-3" />
                <span className="hidden xs:inline">{formatNumber(prompt.views)}</span>
                <span className="xs:hidden">{formatNumber(prompt.views)}</span>
              </div>
              
              <div className="flex items-center gap-1">
                <Copy className="w-3 h-3" />
                <span className="hidden xs:inline">{formatNumber(prompt.copy_count)}</span>
                <span className="xs:hidden">{formatNumber(prompt.copy_count)}</span>
              </div>
            </div>

            {/* 复制按钮 */}
            <Button
              variant="outline"
              size="sm"
              className={cn(
                "h-8 px-2 sm:px-3 text-xs font-medium transition-all min-w-[60px] sm:min-w-[70px]",
                copying && "bg-green-50 border-green-200 text-green-700"
              )}
              onClick={handleCopyClick}
              disabled={copying}
            >
              <Copy className="w-3 h-3 sm:mr-1" />
              <span className="hidden sm:inline ml-1">
                {copying ? "Copied!" : "Copy"}
              </span>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}